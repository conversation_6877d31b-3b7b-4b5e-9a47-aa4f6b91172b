#coding=UTF-8
import glob
import os
from flask import Flask, jsonify, send_file,abort
from AutoConvertMusic import *

# svc_config = {
#     "model_path": r"sovits4.1\logs\44k\G_120000.pth",
#     "config_path": r"sovits4.1\logs\44k\config.json",
#     "cluster_model_path": r"sovits4.1\logs\44k\kmeans_10000.pt", # 这里填聚类模型的路径或特征索引文件的路径，如果没有就cluster_infer_ratio设置为 0
#     "cluster_infer_ratio": 0.5, # 注意：如果没有聚类或特征索引文件，就设置为 0
#     "diffusion_model_path": r"sovits4.1\logs\44k\diffusion\model_50000.pt",
#     "diffusion_config_path": r"sovits4.1\logs\44k\diffusion\config.yaml"
# }

svc_config = {
    "model_path": r"sovits4.1\logs\草神\caoshen1_57000.pth",
    "config_path": r"sovits4.1\logs\草神\config.json",
    "cluster_model_path": r"sovits4.1\logs\yinmei\kmeans_10000.pt", # 这里填聚类模型的路径或特征索引文件的路径，如果没有就cluster_infer_ratio设置为 0
    "cluster_infer_ratio": 0, # 注意：如果没有聚类或特征索引文件，就设置为 0
    "diffusion_model_path": r"sovits4.1\logs\yinmei\diffusion\model_44000.pt",
    "diffusion_config_path": r"sovits4.1\logs\yinmei\diffusion\config.yaml"
}

choose_music_platform = ["kugou", "netease", "bilibili", "youtube"]
default_task_dict = {'en':'bs-roformer-1296','vr1':'6-HP','vr2': 'De-Echo-Normal'}  # 这是走UVR5的默认配置
default_task_dict = {'ms':'bs-roformer-1296','vr1':'6-HP','vr2': 'De-Echo-Normal'}  # 这里ms会走Music-Source-Separation-Training

music_moudle=convert_music(music_platform=choose_music_platform[0], svc_config=svc_config, default_task_dict=default_task_dict)

app = Flask(__name__)
speaker = "草神"

@app.route('/status', methods=['GET'])
def get_status():
    speaker1 = speaker.replace("[中]", "[[]中[]]")
    file_list = glob.glob(f"output/*/*[!Vocals]*_{speaker1}.wav")
    file_name = []
    for f in file_list:
        filename = os.path.basename(f)
        file_name.append(filename.replace(f"_{speaker}.wav", ""))

    # 返回converting和converted的状态
    return jsonify({
        'converting': music_moudle.converting,
        'converted': music_moudle.converted,
        'convertfail': music_moudle.convertfail,
        'converted_file': file_name
    })

@app.route('/append_song/<song_name>', methods=['GET'])
def convert_task_app(song_name): #伊藤サチコ いつも何度でも，"轻舟(dj阿卓版)"
    status,song_name = music_moudle.add_conversion_task(music_info = song_name,
    speaker="草神")
    return jsonify({"status": status, "songName": song_name})

def convert_task(song):
    try:
        print(f"🔄 正在添加转换任务: {song}")
        status, result_name = music_moudle.add_conversion_task(
            music_info=song,
            speaker=speaker
        )
        print(f"✅ 任务添加成功: {song} -> {result_name} (状态: {status})")
    except Exception as e:
        print(f"❌ 任务添加失败: {song} - {e}")
    return status, song

def auto_convert_songs():
    """自动执行转换任务"""
    print("🎵 开始自动转换歌曲...")

    # 测试歌曲列表
    test_songs = [
        "轻舟",
        # "伊藤サチコ いつも何度でも",
        # "轻舟(dj阿卓版)"
    ]

    for song in test_songs:
        # convert_task(song)
        # only_download(song)
        only_search(song)

        # url = "https://gateway.kugou.com/v5/url?dfid=3f0Lfi0SSmUE3A4e6p4Gzzbk&mid=f804fe654348c2d194390d9c3d942b6c&uuid=feee6e61e7f6f682123605ca30757522&appid=3116&clientver=11040&userid=0&clienttime=1753339380&album_id=0&area_code=1&hash=cc935b5f2dac38763b1c40e2849ae312&ssa_flag=is_fromtrack&version=11040&page_id=967177915&quality=128&album_audio_id=0&behavior=play&pid=411&cmd=26&pidversion=3001&IsFreePart=0&ppage_id=356753938%2C823673182%2C967485191&cdnBackup=1&kcard=0&module=&key=d6099df8ef981c453c3d2cfd14e4f03c&signature=b4a15e69d74f396262aa48c532d1c3ca"
        # url0 = "http://localhost:3001/song/url?hash=D792570A18CE4264876FE470E7448208"
        # response = music_moudle.platform.kugou.get(url).text
        # song_data = json.loads(response)

    print("🎉 自动转换任务添加完成！")

def only_search(song):
    try:
        print(f"🔄 正在搜索歌曲: {song}")
        search_results = music_moudle.platform.search_music(song, limit=10, return_list=True)
        print(f"🔍 搜索结果: {search_results}")
    except Exception as e:
        print(f"❌ 搜索失败: {song} - {e}")

def only_download(song):
    try:
        print(f"🔄 正在下载歌曲: {song}")
        display_name, file_path = music_moudle.platform.search_and_download_music(song)
        print(f"✅ 下载成功: {display_name} -> {file_path}")
    except Exception as e:
        print(f"❌ 下载失败: {song} - {e}")

@app.route('/get_audio/<song_name>', methods=['GET'])
def get_audio(song_name):
    search_pattern = os.path.join(f"output/{song_name}/{song_name}*.wav")
    list = glob.glob(search_pattern)
    if len(list)>0:
        matching_files = list[0]
        try:
            return send_file(matching_files, as_attachment=False)
        except:
            abort(404, description="Audio file not found")

if __name__ == '__main__':
    # 启动时自动执行转换任务
    import threading
    import time

    def delayed_auto_convert():
        """延迟执行自动转换，等待服务完全启动"""
        time.sleep(3)  # 等待3秒让服务完全启动
        auto_convert_songs()

    # 在后台线程中执行自动转换
    auto_thread = threading.Thread(target=delayed_auto_convert, daemon=True)
    auto_thread.start()

    print("🚀 启动Web_through服务...")
    print("📡 服务地址: http://0.0.0.0:1717")
    print("🔗 状态接口: http://0.0.0.0:1717/status")

    app.run(host="0.0.0.0", port=1717)
