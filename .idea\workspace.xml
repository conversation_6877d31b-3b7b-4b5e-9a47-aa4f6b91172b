<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="99324431-31c2-43d9-8bcb-087627c9ddaa" name="更改" comment="整合酷狗，初步提交">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AutoConvertMusic.py" beforeDir="false" afterPath="$PROJECT_DIR$/AutoConvertMusic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kugou.py" beforeDir="false" afterPath="$PROJECT_DIR$/kugou.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kugou.txt" beforeDir="false" afterPath="$PROJECT_DIR$/kugou.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager" show_ignored="true">
    <option name="groupingKeys">
      <option value="module" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 4
}]]></component>
  <component name="ProjectId" id="30OkDDGuCwq07KSaj4Wdvl8oHDk" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "codeReviewSummary": "[]",
    "last_opened_file_path": "G:/comfyuiBL/Auto-Convert-Music-master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="Babashka" factoryName="BabashkaLocalRepl" activateToolWindowBeforeRun="false">
      <setting name="displayName" value="" />
      <setting name="bbPath" value="" />
      <setting name="parameters" value="" />
      <option name="PARENT_ENVS" value="true" />
      <setting name="workingDir" value="" />
      <setting name="focusEditor" value="false" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="ClojureREPL" factoryName="Local" activateToolWindowBeforeRun="false">
      <method v="2" />
    </configuration>
    <configuration default="true" type="ClojureREPL" factoryName="Remote" activateToolWindowBeforeRun="false">
      <setting name="displayName" value="" />
      <setting name="host" value="" />
      <setting name="port" value="0" />
      <setting name="replType" value="SOCKET" />
      <setting name="configType" value="SPECIFY" />
      <setting name="replPortFileType" value="STANDARD" />
      <setting name="customPortFile" value="" />
      <setting name="fixLineNumbers" value="false" />
      <setting name="focusEditor" value="false" />
      <setting name="urlFile" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Auto-Convert-Music-master" type="PythonConfigurationType" factoryName="Python">
      <module name="Auto-Convert-Music-master" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Web_through.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="99324431-31c2-43d9-8bcb-087627c9ddaa" name="更改" comment="" />
      <created>1753505892252</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753505892252</updated>
      <workItem from="1753505893405" duration="1483000" />
    </task>
    <task id="LOCAL-00001" summary="整合酷狗，初步提交">
      <option name="closed" value="true" />
      <created>1753506709356</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753506709356</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="整合酷狗，初步提交" />
    <option name="LAST_COMMIT_MESSAGE" value="整合酷狗，初步提交" />
  </component>
</project>